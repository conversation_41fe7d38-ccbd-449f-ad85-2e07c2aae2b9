from PyInstaller.utils.hooks import copy_metadata, collect_data_files
import streamlit
import os

# 复制Streamlit的元数据
datas = copy_metadata("streamlit")

# 复制Streamlit的静态文件
streamlit_dir = os.path.dirname(streamlit.__file__)
datas += [
    (os.path.join(streamlit_dir, 'static'), 'streamlit/static'),
    (os.path.join(streamlit_dir, 'runtime'), 'streamlit/runtime'),
]

# 收集所有Streamlit数据文件
datas += collect_data_files("streamlit")
