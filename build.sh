#!/bin/bash

echo "BSA应用构建工具 (Linux/macOS)"
echo "================================"

# 检查Python环境
echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请确保Python3已安装"
    exit 1
fi

python3 --version

# 安装依赖包
echo ""
echo "正在安装依赖包..."
python3 -m pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "警告: 依赖包安装可能有问题，继续构建..."
fi

# 安装PyInstaller
echo ""
echo "正在安装PyInstaller..."
python3 -m pip install pyinstaller
if [ $? -ne 0 ]; then
    echo "错误: PyInstaller安装失败"
    exit 1
fi

# 清理旧的构建文件
echo ""
echo "正在清理旧的构建文件..."
rm -rf build dist __pycache__
find . -name "*.pyc" -delete

# 构建可执行文件
echo ""
echo "开始构建可执行文件..."
echo "================================"
python3 -m PyInstaller --clean bsa_app.spec
if [ $? -ne 0 ]; then
    echo "错误: 构建失败"
    exit 1
fi

echo ""
echo "================================"
echo "构建完成！"
echo "可执行文件位置: $(pwd)/dist/"
echo ""
echo "使用说明:"
echo "1. 进入dist目录: cd dist"
echo "2. 运行可执行文件: ./bsa_app"
echo "3. 在浏览器中访问 http://localhost:8501"
echo ""

# 设置可执行权限
chmod +x dist/bsa_app
echo "已设置可执行权限"
