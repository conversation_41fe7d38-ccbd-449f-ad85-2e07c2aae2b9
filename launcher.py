#!/usr/bin/env python3
"""
BSA应用启动器
用于在PyInstaller打包后正确启动Streamlit应用
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主启动函数"""
    # 获取可执行文件所在目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        app_dir = Path(sys.executable).parent
    else:
        # 如果是开发环境
        app_dir = Path(__file__).parent
    
    # 切换到应用目录
    os.chdir(app_dir)
    
    # 设置环境变量
    os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
    os.environ['STREAMLIT_SERVER_PORT'] = '8501'
    os.environ['STREAMLIT_SERVER_ADDRESS'] = 'localhost'
    
    # 构建Streamlit命令
    streamlit_script = app_dir / 'bsa_ui.py'
    
    # 启动Streamlit应用
    try:
        print("正在启动BSA数据生成器...")
        print(f"应用目录: {app_dir}")
        print(f"配置文件: {app_dir / 'config.json'}")
        print("请在浏览器中访问: http://localhost:8501")
        print("按 Ctrl+C 退出应用")
        print("-" * 50)
        
        # 使用streamlit run命令启动
        cmd = [
            sys.executable, '-m', 'streamlit', 'run', 
            str(streamlit_script),
            '--server.headless', 'true',
            '--server.port', '8501',
            '--server.address', 'localhost',
            '--browser.gatherUsageStats', 'false'
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n应用已退出")
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
