@echo off
chcp 65001 >nul
echo BSA应用构建工具 (Windows)
echo ================================

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo.
echo 正在安装依赖包...
python -m pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 警告: 依赖包安装可能有问题，继续构建...
)

echo.
echo 正在安装PyInstaller...
python -m pip install pyinstaller
if %errorlevel% neq 0 (
    echo 错误: PyInstaller安装失败
    pause
    exit /b 1
)

echo.
echo 正在清理旧的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__

echo.
echo 开始构建可执行文件...
echo ================================
python -m PyInstaller --clean bsa_app.spec
if %errorlevel% neq 0 (
    echo 错误: 构建失败
    pause
    exit /b 1
)

echo.
echo ================================
echo 构建完成！
echo 可执行文件位置: %cd%\dist\
echo.
echo 使用说明:
echo 1. 进入dist目录
echo 2. 双击运行bsa_app.exe
echo 3. 在浏览器中访问 http://localhost:8501
echo.
pause
