#!/usr/bin/env python3
"""
简化的BSA应用启动器
直接运行bsa_ui模块
"""

import os
import sys
import threading
import time
import webbrowser
from pathlib import Path

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(5)
    try:
        webbrowser.open('http://localhost:8501')
        print("✓ 浏览器已打开")
    except:
        print("请手动打开浏览器访问: http://localhost:8501")

if __name__ == "__main__":
    print("BSA数据生成器")
    print("=" * 40)
    print("正在启动...")
    print("-" * 40)
    
    # 设置工作目录
    if getattr(sys, 'frozen', False):
        os.chdir(Path(sys.executable).parent)
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 直接运行streamlit
    os.system('streamlit run bsa_ui.py --server.headless true --server.port 8501 --browser.gatherUsageStats false')
