BSA数据生成器 - 可执行文件构建说明
=====================================

## 构建环境要求
- Python 3.8 或更高版本
- 已安装项目依赖包 (requirements.txt)
- 足够的磁盘空间 (约500MB用于构建过程)

## 快速构建 (推荐)

### Windows用户:
1. 双击运行 build.bat
2. 等待构建完成
3. 在dist目录中找到bsa_app.exe

### Linux/macOS用户:
1. 在终端中运行: ./build.sh
2. 等待构建完成
3. 在dist目录中找到bsa_app

## 手动构建步骤

1. 安装PyInstaller:
   pip install pyinstaller

2. 安装项目依赖:
   pip install -r requirements.txt

3. 清理旧构建文件:
   - 删除 build/ 目录
   - 删除 dist/ 目录
   - 删除 __pycache__/ 目录

4. 执行构建:
   pyinstaller --clean bsa_app.spec

5. 构建完成后，可执行文件位于 dist/ 目录中

## 使用构建脚本 (详细输出)

如果需要查看详细的构建过程，可以使用:
python build.py

这个脚本会显示完整的构建过程信息。

## 构建后的文件结构

dist/
├── bsa_app(.exe)      # 主可执行文件
├── config.json        # 配置文件
├── bsa_ui.py          # UI模块
└── bsa_core.py        # 核心模块

## 运行可执行文件

1. 进入dist目录
2. 运行可执行文件:
   - Windows: 双击 bsa_app.exe
   - Linux/macOS: ./bsa_app
3. 程序启动后会显示访问地址
4. 在浏览器中打开 http://localhost:8501

## 注意事项

1. 首次运行可能需要较长时间启动
2. 确保8501端口未被占用
3. config.json文件必须与可执行文件在同一目录
4. 如果遇到权限问题，请以管理员身份运行
5. 构建的可执行文件包含了所有依赖，无需额外安装Python环境

## 故障排除

1. 如果构建失败，请检查:
   - Python版本是否符合要求
   - 是否有足够的磁盘空间
   - 网络连接是否正常 (用于下载依赖)

2. 如果运行失败，请检查:
   - config.json文件是否存在
   - 端口8501是否被占用
   - 防火墙是否阻止了程序

3. 如果浏览器无法访问，请尝试:
   - 检查控制台输出的实际端口号
   - 尝试访问 http://127.0.0.1:8501
   - 关闭防火墙或添加例外

## 文件说明

- bsa_app.spec: PyInstaller配置文件
- launcher.py: 启动器脚本
- build.py: Python构建脚本
- build.bat: Windows批处理构建脚本
- build.sh: Linux/macOS Shell构建脚本
