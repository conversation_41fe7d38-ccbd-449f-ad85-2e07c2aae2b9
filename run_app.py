#!/usr/bin/env python3
"""
BSA应用运行脚本
用于启动Streamlit应用
"""

import streamlit.web.cli as stcli
import os
import sys


def resolve_path(path):
    """解析文件路径"""
    resolved_path = os.path.abspath(os.path.join(os.getcwd(), path))
    return resolved_path


if __name__ == "__main__":
    print("BSA数据生成器")
    print("=" * 40)
    print("正在启动Web应用...")
    print("请稍候，首次启动可能需要较长时间...")
    print("启动后请在浏览器中访问: http://localhost:8501")
    print("-" * 40)

    # 设置Streamlit命令行参数
    sys.argv = [
        "streamlit",
        "run",
        resolve_path("bsa_ui.py"),  # BSA应用的主文件
        "--global.developmentMode=false",
        "--server.headless=true",
        "--server.port=8501",
        "--server.address=localhost",
        "--browser.gatherUsageStats=false",
    ]

    # 启动Streamlit应用
    sys.exit(stcli.main())
