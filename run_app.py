#!/usr/bin/env python3
"""
BSA应用运行脚本
用于启动Streamlit应用
"""

import os
import sys
import threading
import time
import webbrowser
from pathlib import Path


def resolve_path(path):
    """解析文件路径"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        base_path = Path(sys.executable).parent
    else:
        # 如果是开发环境
        base_path = Path(__file__).parent

    resolved_path = base_path / path
    return str(resolved_path.absolute())


def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(5)  # 等待Streamlit服务启动
    try:
        webbrowser.open('http://localhost:8501')
        print("✓ 已尝试自动打开浏览器")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")


if __name__ == "__main__":
    print("BSA数据生成器")
    print("=" * 40)
    print("正在启动Web应用...")
    print("请稍候，首次启动可能需要较长时间...")
    print("启动后请在浏览器中访问: http://localhost:8501")
    print("-" * 40)

    # 启动浏览器打开线程
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()

    try:
        # 直接导入并运行主应用
        sys.path.insert(0, os.path.dirname(resolve_path("bsa_ui.py")))

        # 设置Streamlit配置
        os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
        os.environ['STREAMLIT_SERVER_PORT'] = '8501'
        os.environ['STREAMLIT_SERVER_ADDRESS'] = 'localhost'

        # 使用streamlit命令行接口
        import streamlit.web.cli as stcli

        # 设置命令行参数
        sys.argv = [
            "streamlit",
            "run",
            resolve_path("bsa_ui.py"),
            "--global.developmentMode=false",
            "--server.headless=true",
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false",
        ]

        # 启动Streamlit应用
        stcli.main()

    except KeyboardInterrupt:
        print("\n应用已退出")
    except Exception as e:
        print(f"启动失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查8501端口是否被占用")
        print("2. 确保config.json文件存在")
        print("3. 尝试以管理员权限运行")
        input("\n按回车键退出...")
