#!/usr/bin/env python3
"""
BSA应用构建脚本 - 跨平台版本
支持Windows和macOS构建
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path

def check_requirements():
    """检查构建环境"""
    print("=== 检查构建环境 ===")

    # 检查操作系统
    system = platform.system()
    print(f"操作系统: {system}")

    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

    # 检查必要文件
    required_files = ['bsa_ui.py', 'bsa_core.py', 'config.json', 'requirements.txt', 'bsa_app.spec', 'run_app.py']
    missing_files = []

    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"✓ 找到文件: {file}")

    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False

    print("✓ 所有必要文件都存在")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n=== 安装依赖包 ===")
    try:
        # 安装项目依赖
        print("正在安装项目依赖...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
        print("✓ 项目依赖安装成功")

        # 安装PyInstaller
        print("正在安装PyInstaller...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def clean_build_dirs():
    """清理构建目录"""
    print("\n=== 清理构建目录 ===")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"✓ 清理目录: {dir_name}")
    
    # 清理.pyc文件
    for pyc_file in Path('.').rglob('*.pyc'):
        pyc_file.unlink()
        print(f"✓ 清理文件: {pyc_file}")

def build_executable():
    """构建可执行文件"""
    print("\n=== 开始构建可执行文件 ===")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'bsa_app.spec']
        print(f"执行命令: {' '.join(cmd)}")
        
        # 执行构建命令，显示详细输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时显示构建输出
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("✓ 构建成功完成")
            return True
        else:
            print(f"❌ 构建失败，退出码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def verify_build():
    """验证构建结果"""
    print("\n=== 验证构建结果 ===")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 检查可执行文件
    if sys.platform.startswith('win'):
        exe_name = 'bsa_app.exe'
    else:
        exe_name = 'bsa_app'
    
    exe_path = dist_dir / exe_name
    if not exe_path.exists():
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    print(f"✓ 找到可执行文件: {exe_path}")
    print(f"✓ 文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
    
    # 检查配置文件
    config_path = dist_dir / 'config.json'
    if config_path.exists():
        print(f"✓ 配置文件已包含: {config_path}")
    else:
        print("⚠️ 配置文件未找到，可能需要手动复制")
    
    return True

def main():
    """主构建流程"""
    system = platform.system()
    print(f"BSA应用构建工具 - {system}")
    print("=" * 50)

    # 检查环境
    if not check_requirements():
        print("❌ 环境检查失败，请解决问题后重试")
        return False

    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        return False

    # 清理构建目录
    clean_build_dirs()

    # 构建可执行文件
    if not build_executable():
        print("❌ 构建失败")
        return False

    # 验证构建结果
    if not verify_build():
        print("❌ 构建验证失败")
        return False

    print("\n" + "=" * 50)
    print("🎉 构建完成！")
    print(f"可执行文件位置: {Path('dist').absolute()}")

    if system == "Windows":
        exe_name = "bsa_app.exe"
    else:
        exe_name = "bsa_app"

    print("\n使用说明:")
    print("1. 进入dist目录")
    print(f"2. 运行 {exe_name}")
    print("3. 程序会自动打开浏览器访问应用")
    print("4. 如果浏览器未自动打开，请手动访问 http://localhost:8501")

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
