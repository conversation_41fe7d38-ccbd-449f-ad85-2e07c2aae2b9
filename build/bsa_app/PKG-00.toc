('/Users/<USER>/Desktop/BSA-Test/build/bsa_app/bsa_app.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', '/Users/<USER>/Desktop/BSA-Test/build/bsa_app/PYZ-00.pyz', 'PYZ'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Desktop/BSA-Test/build/bsa_app/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Desktop/BSA-Test/build/bsa_app/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Desktop/BSA-Test/build/bsa_app/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Desktop/BSA-Test/build/bsa_app/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('run_app', '/Users/<USER>/Desktop/BSA-Test/run_app.py', 'PYSOURCE'),
  ('libmenu.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libmenu.6.dylib',
   'BINARY'),
  ('libtinfo.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libtinfo.6.dylib',
   'BINARY'),
  ('libquadmath.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libquadmath.0.dylib',
   'BINARY'),
  ('libmenuw.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libmenuw.6.dylib',
   'BINARY'),
  ('libmpdec.4.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libmpdec.4.0.0.dylib',
   'BINARY'),
  ('libomp.dylib', '/opt/miniconda3/envs/web-bsa/lib/libomp.dylib', 'BINARY'),
  ('libopenblas.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopenblas.0.dylib',
   'BINARY'),
  ('libexpat.1.10.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libexpat.1.10.1.dylib',
   'BINARY'),
  ('libgfortran.5.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libgfortran.5.dylib',
   'BINARY'),
  ('libpanelw.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libpanelw.6.dylib',
   'BINARY'),
  ('libgcc_s.1.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libgcc_s.1.1.dylib',
   'BINARY'),
  ('libpython3.13.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libpython3.13.dylib',
   'BINARY'),
  ('libpanel.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libpanel.6.dylib',
   'BINARY'),
  ('libsqlite3.3.50.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libsqlite3.3.50.1.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libtinfow.6.dylib',
   'BINARY'),
  ('libbz2.1.0.8.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libbz2.1.0.8.dylib',
   'BINARY'),
  ('libtk8.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libtk8.6.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libssl.3.dylib',
   'BINARY'),
  ('libz.1.3.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libz.1.3.1.dylib',
   'BINARY'),
  ('libffi.8.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libffi.8.dylib',
   'BINARY'),
  ('libtcl8.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libtcl8.6.dylib',
   'BINARY'),
  ('libncurses.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libncurses.6.dylib',
   'BINARY'),
  ('liblzma.5.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/liblzma.5.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libreadline.8.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libreadline.8.2.dylib',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libncursesw.6.dylib',
   'BINARY'),
  ('libc++.1.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libc++.1.0.dylib',
   'BINARY'),
  ('libformw.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libformw.6.dylib',
   'BINARY'),
  ('libhistory.8.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libhistory.8.2.dylib',
   'BINARY'),
  ('libform.6.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libform.6.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python.2000.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2000.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2000.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2000.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2000.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2000.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2000.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2000.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/libarrow_python.2000.0.0.dylib',
   'BINARY'),
  ('Cryptodome/Hash/_MD4.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_MD4.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_chacha20.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_chacha20.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_MD5.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_MD5.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_SHA1.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_SHA1.abi3.so',
   'BINARY'),
  ('Cryptodome/Util/_cpuid_c.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Util/_cpuid_c.abi3.so',
   'BINARY'),
  ('Cryptodome/PublicKey/_ed448.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/PublicKey/_ed448.abi3.so',
   'BINARY'),
  ('Cryptodome/Math/_modexp.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Math/_modexp.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_pkcs1_decode.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_pkcs1_decode.abi3.so',
   'BINARY'),
  ('Cryptodome/PublicKey/_ec_ws.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/PublicKey/_ec_ws.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_des3.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_des3.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_SHA512.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_SHA512.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_arc2.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_arc2.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_des.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_des.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_SHA384.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_SHA384.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_ecb.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_ecb.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_ARC4.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_ARC4.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_SHA224.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_SHA224.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_ctr.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_ctr.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_cfb.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_cfb.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_BLAKE2b.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_BLAKE2b.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_ghash_portable.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_ghash_portable.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_eksblowfish.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_eksblowfish.abi3.so',
   'BINARY'),
  ('Cryptodome/PublicKey/_curve25519.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/PublicKey/_curve25519.abi3.so',
   'BINARY'),
  ('Cryptodome/PublicKey/_ed25519.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/PublicKey/_ed25519.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_cbc.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_cbc.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_ofb.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_ofb.abi3.so',
   'BINARY'),
  ('Cryptodome/PublicKey/_curve448.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/PublicKey/_curve448.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_Salsa20.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_Salsa20.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_SHA256.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_SHA256.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_keccak.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_keccak.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_ocb.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_ocb.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_MD2.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_MD2.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_poly1305.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_poly1305.abi3.so',
   'BINARY'),
  ('Cryptodome/Protocol/_scrypt.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Protocol/_scrypt.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_RIPEMD160.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_RIPEMD160.abi3.so',
   'BINARY'),
  ('Cryptodome/Hash/_BLAKE2s.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Hash/_BLAKE2s.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_blowfish.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_blowfish.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_aes.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_aes.abi3.so',
   'BINARY'),
  ('Cryptodome/Cipher/_raw_cast.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Cipher/_raw_cast.abi3.so',
   'BINARY'),
  ('Cryptodome/Util/_strxor.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/Cryptodome/Util/_strxor.abi3.so',
   'BINARY'),
  ('pyarrow/lib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/lib.cpython-313-darwin.so',
   'BINARY'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_posixshmem.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_multiprocessing.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/pyexpat.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_scproxy.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/termios.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_ssl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/mmap.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_ctypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_queue.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/_cffi_backend.cpython-313-darwin.so',
   'EXTENSION'),
  ('lz4/_version.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4/_version.cpython-313-darwin.so',
   'EXTENSION'),
  ('lz4/block/_block.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4/block/_block.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/readline.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_sqlite3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_elementtree.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_asyncio.cpython-313-darwin.so',
   'EXTENSION'),
  ('zstandard/_cffi.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/zstandard/_cffi.cpython-313-darwin.so',
   'EXTENSION'),
  ('zstandard/backend_c.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/zstandard/backend_c.cpython-313-darwin.so',
   'EXTENSION'),
  ('_brotli.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/_brotli.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/mtrand.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/bit_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/_sfc64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/_philox.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/_pcg64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/_mt19937.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/_common.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/random/_bounded_integers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('yaml/_yaml.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/yaml/_yaml.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/cmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/writers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_compute.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_compute.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/gandiva.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/gandiva.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_substrait.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_substrait.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_s3fs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_s3fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet_encryption.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_orc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_hdfs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_hdfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_gcsfs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_gcsfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_fs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_flight.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_flight.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_orc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_csv.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_azurefs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_azurefs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_acero.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_acero.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_feather.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_feather.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_uuid.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/_ccallback_c.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/_lib/_ccallback_c.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_tools.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/csgraph/_tools.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_reordering.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/csgraph/_reordering.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_matching.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/csgraph/_matching.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_flow.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/csgraph/_flow.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_min_spanning_tree.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/csgraph/_min_spanning_tree.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_traversal.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/csgraph/_traversal.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/csgraph/_shortest_path.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/csgraph/_shortest_path.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_fblas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_fblas.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_flapack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_flapack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/cython_lapack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/cython_lapack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/cython_blas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/cython_blas.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_decomp_update.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_decomp_update.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/_ellip_harm_2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/_ellip_harm_2.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_highspy/_core.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_highspy/_core.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_highspy/_highs_options.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_highspy/_highs_options.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_direct.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_direct.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_rgi_cython.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_rgi_cython.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_interpnd.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_interpnd.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_bspl.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_bspl.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_rbfinterp_pythran.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_rbfinterp_pythran.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_ppoly.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_ppoly.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_dierckx.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_dierckx.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_dfitpack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_dfitpack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/interpolate/_fitpack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/interpolate/_fitpack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/transform/_rotation.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/spatial/transform/_rotation.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_distance_pybind.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/spatial/_distance_pybind.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_hausdorff.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/spatial/_hausdorff.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_distance_wrap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/spatial/_distance_wrap.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_voronoi.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/spatial/_voronoi.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_qhull.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/spatial/_qhull.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/spatial/_ckdtree.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/spatial/_ckdtree.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_pava_pybind.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_pava_pybind.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_lsq/givens_elimination.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_lsq/givens_elimination.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_lsap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_lsap.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_decomp_interpolative.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_decomp_interpolative.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_bglu_dense.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_bglu_dense.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_cython_nnls.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_cython_nnls.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_slsqp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_slsqp.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_zeros.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_zeros.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_minpack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_minpack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_trlib/_trlib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_trlib/_trlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_cobyla.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_cobyla.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_moduleTNC.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_moduleTNC.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_lbfgsb.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_lbfgsb.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/optimize/_group_columns.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/optimize/_group_columns.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_lsoda.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/integrate/_lsoda.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_dop.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/integrate/_dop.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_vode.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/integrate/_vode.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_quadpack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/integrate/_quadpack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/integrate/_odepack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/integrate/_odepack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/_gufuncs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/_gufuncs.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/_special_ufuncs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/_special_ufuncs.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/_comb.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/_comb.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/_specfun.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/_specfun.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/_ufuncs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/_ufuncs.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/_ufuncs_cxx.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/_ufuncs_cxx.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_linalg_pythran.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_linalg_pythran.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_matfuncs_expm.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_matfuncs_expm.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_matfuncs_sqrtm_triu.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_matfuncs_sqrtm_triu.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_decomp_lu_cython.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_decomp_lu_cython.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/fft/_pocketfft/pypocketfft.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/fft/_pocketfft/pypocketfft.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/_uarray/_uarray.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/_lib/_uarray/_uarray.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_solve_toeplitz.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_solve_toeplitz.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/linalg/_cythonized_array_utils.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/linalg/_cythonized_array_utils.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_zpropack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/linalg/_propack/_zpropack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_cpropack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/linalg/_propack/_cpropack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_dpropack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/linalg/_propack/_dpropack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_propack/_spropack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/linalg/_propack/_spropack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/linalg/_dsolve/_superlu.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/linalg/_dsolve/_superlu.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/_csparsetools.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/_csparsetools.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/sparse/_sparsetools.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/sparse/_sparsetools.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/ndimage/_rank_filter_1d.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/ndimage/_rank_filter_1d.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/ndimage/_nd_image.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/ndimage/_nd_image.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/ndimage/_ni_label.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/ndimage/_ni_label.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_qmc_cy.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_qmc_cy.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_sobol.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_sobol.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_stats_pythran.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_stats_pythran.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_rcont/rcont.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_rcont/rcont.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_mvn.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_mvn.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_ansari_swilk_statistics.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_ansari_swilk_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_levy_stable/levyst.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_levy_stable/levyst.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_biasedurn.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_biasedurn.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/stats/_stats.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/stats/_stats.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/special/cython_special.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/cython_special.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/_fpumode.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/_lib/_fpumode.cpython-313-darwin.so',
   'EXTENSION'),
  ('scipy/_lib/messagestream.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/_lib/messagestream.cpython-313-darwin.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/markupsafe/_speedups.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/window/indexers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/window/aggregations.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/period.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslibs/base.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/tslib.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/testing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/sparse.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/sas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/reshape.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/properties.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/parsers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/pandas_parser.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/ops.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/missing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/lib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/lib.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/join.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/interval.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/internals.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/indexing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/index.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/hashtable.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/hashing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/groupby.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/byteswap.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/arrays.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/_libs/algos.cpython-313-darwin.so',
   'EXTENSION'),
  ('_watchdog_fsevents.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/_watchdog_fsevents.cpython-313-darwin.so',
   'EXTENSION'),
  ('google/_upb/_message.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/google/_upb/_message.cpython-313-darwin.so',
   'EXTENSION'),
  ('rpds/rpds.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/rpds/rpds.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_testcapi.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_testcapi.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_curses.cpython-313-darwin.so',
   'EXTENSION'),
  ('tornado/speedups.abi3.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/tornado/speedups.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('libarrow.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libarrow.2000.dylib',
   'BINARY'),
  ('libparquet.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libparquet.2000.dylib',
   'BINARY'),
  ('libyaml-0.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libyaml-0.2.dylib',
   'BINARY'),
  ('libarrow_substrait.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libarrow_substrait.2000.dylib',
   'BINARY'),
  ('libarrow_dataset.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libarrow_dataset.2000.dylib',
   'BINARY'),
  ('libarrow_acero.2000.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libarrow_acero.2000.dylib',
   'BINARY'),
  ('scipy/special/libsf_error_state.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/scipy/special/libsf_error_state.dylib',
   'BINARY'),
  ('libprotobuf.29.3.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libprotobuf.29.3.0.dylib',
   'BINARY'),
  ('libazure-storage-files-datalake.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libazure-storage-files-datalake.dylib',
   'BINARY'),
  ('libre2.11.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libre2.11.dylib',
   'BINARY'),
  ('libaws-cpp-sdk-core.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-cpp-sdk-core.dylib',
   'BINARY'),
  ('libzstd.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libzstd.1.dylib',
   'BINARY'),
  ('libaws-crt-cpp.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-crt-cpp.dylib',
   'BINARY'),
  ('libopentelemetry_trace.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_trace.dylib',
   'BINARY'),
  ('libopentelemetry_resources.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_resources.dylib',
   'BINARY'),
  ('libgoogle_cloud_cpp_storage.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libgoogle_cloud_cpp_storage.2.dylib',
   'BINARY'),
  ('libabsl_time_zone.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_time_zone.2501.0.0.dylib',
   'BINARY'),
  ('liblz4.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/liblz4.1.dylib',
   'BINARY'),
  ('libutf8proc.3.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libutf8proc.3.dylib',
   'BINARY'),
  ('libabsl_status.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_status.2501.0.0.dylib',
   'BINARY'),
  ('libbrotlidec.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libbrotlidec.1.dylib',
   'BINARY'),
  ('libazure-storage-blobs.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libazure-storage-blobs.dylib',
   'BINARY'),
  ('libopentelemetry_exporter_otlp_http.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_exporter_otlp_http.dylib',
   'BINARY'),
  ('libgoogle_cloud_cpp_common.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libgoogle_cloud_cpp_common.2.dylib',
   'BINARY'),
  ('libopentelemetry_otlp_recordable.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_otlp_recordable.dylib',
   'BINARY'),
  ('libazure-identity.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libazure-identity.dylib',
   'BINARY'),
  ('libopentelemetry_logs.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_logs.dylib',
   'BINARY'),
  ('liborc.dylib', '/opt/miniconda3/envs/web-bsa/lib/liborc.dylib', 'BINARY'),
  ('libglog.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libglog.2.dylib',
   'BINARY'),
  ('libabsl_time.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_time.2501.0.0.dylib',
   'BINARY'),
  ('libbrotlienc.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libbrotlienc.1.dylib',
   'BINARY'),
  ('libazure-core.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libazure-core.dylib',
   'BINARY'),
  ('libopentelemetry_exporter_otlp_http_client.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_exporter_otlp_http_client.dylib',
   'BINARY'),
  ('libopentelemetry_exporter_ostream_span.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_exporter_ostream_span.dylib',
   'BINARY'),
  ('libaws-cpp-sdk-identity-management.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-cpp-sdk-identity-management.dylib',
   'BINARY'),
  ('libaws-cpp-sdk-s3.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-cpp-sdk-s3.dylib',
   'BINARY'),
  ('libopentelemetry_exporter_otlp_http_log.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_exporter_otlp_http_log.dylib',
   'BINARY'),
  ('libsnappy.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libsnappy.1.dylib',
   'BINARY'),
  ('libopentelemetry_proto.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_proto.dylib',
   'BINARY'),
  ('libthrift.0.21.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libthrift.0.21.0.dylib',
   'BINARY'),
  ('libabsl_str_format_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_str_format_internal.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_hash.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_hash.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_spinlock_wait.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_spinlock_wait.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_cord.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_cord.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_cord_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_cord_internal.2501.0.0.dylib',
   'BINARY'),
  ('libutf8_validity.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libutf8_validity.dylib',
   'BINARY'),
  ('libabsl_raw_hash_set.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_raw_hash_set.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_synchronization.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_synchronization.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_statusor.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_statusor.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_strings.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_strings.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_check_op.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_check_op.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_message.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_message.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_nullguard.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_nullguard.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_die_if_null.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_die_if_null.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_cordz_info.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_cordz_info.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_conditions.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_conditions.2501.0.0.dylib',
   'BINARY'),
  ('libazure-storage-common.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libazure-storage-common.dylib',
   'BINARY'),
  ('libaws-c-common.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-common.1.dylib',
   'BINARY'),
  ('libaws-c-event-stream.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-event-stream.1.0.0.dylib',
   'BINARY'),
  ('libcurl.4.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libcurl.4.dylib',
   'BINARY'),
  ('libaws-c-mqtt.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-mqtt.1.0.0.dylib',
   'BINARY'),
  ('libaws-c-s3.0unstable.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-s3.0unstable.dylib',
   'BINARY'),
  ('libaws-checksums.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-checksums.1.0.0.dylib',
   'BINARY'),
  ('libaws-c-io.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-io.1.0.0.dylib',
   'BINARY'),
  ('libaws-c-sdkutils.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-sdkutils.1.0.0.dylib',
   'BINARY'),
  ('libaws-c-cal.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-cal.1.0.0.dylib',
   'BINARY'),
  ('libaws-c-auth.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-auth.1.0.0.dylib',
   'BINARY'),
  ('libaws-c-http.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-http.1.0.0.dylib',
   'BINARY'),
  ('libopentelemetry_common.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_common.dylib',
   'BINARY'),
  ('libgoogle_cloud_cpp_rest_internal.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libgoogle_cloud_cpp_rest_internal.2.dylib',
   'BINARY'),
  ('libabsl_strings_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_strings_internal.2501.0.0.dylib',
   'BINARY'),
  ('libcrc32c.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libcrc32c.1.dylib',
   'BINARY'),
  ('libabsl_crc32c.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_crc32c.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_leak_check.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_leak_check.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_strerror.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_strerror.2501.0.0.dylib',
   'BINARY'),
  ('libbrotlicommon.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libbrotlicommon.1.dylib',
   'BINARY'),
  ('libopentelemetry_metrics.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_metrics.dylib',
   'BINARY'),
  ('libgflags.2.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libgflags.2.2.dylib',
   'BINARY'),
  ('libopentelemetry_http_client_curl.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libopentelemetry_http_client_curl.dylib',
   'BINARY'),
  ('libaws-cpp-sdk-sts.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-cpp-sdk-sts.dylib',
   'BINARY'),
  ('libaws-cpp-sdk-cognito-identity.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-cpp-sdk-cognito-identity.dylib',
   'BINARY'),
  ('libabsl_int128.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_int128.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_low_level_hash.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_low_level_hash.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_city.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_city.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_raw_logging_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_raw_logging_internal.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_crc_cord_state.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_crc_cord_state.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_stacktrace.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_stacktrace.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_kernel_timeout_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_kernel_timeout_internal.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_base.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_base.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_malloc_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_malloc_internal.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_tracing_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_tracing_internal.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_globals.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_globals.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_proto.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_proto.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_globals.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_globals.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_examine_stack.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_examine_stack.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_structured_proto.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_structured_proto.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_format.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_format.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_internal_log_sink_set.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_internal_log_sink_set.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_cordz_handle.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_cordz_handle.2501.0.0.dylib',
   'BINARY'),
  ('libxml2.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libxml2.2.dylib',
   'BINARY'),
  ('libgssapi_krb5.2.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libgssapi_krb5.2.2.dylib',
   'BINARY'),
  ('libnghttp2.14.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libnghttp2.14.dylib',
   'BINARY'),
  ('libssh2.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libssh2.1.dylib',
   'BINARY'),
  ('libaws-c-compression.1.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libaws-c-compression.1.0.0.dylib',
   'BINARY'),
  ('libabsl_crc_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_crc_internal.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_crc_cpu_detect.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_crc_cpu_detect.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_symbolize.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_symbolize.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_log_sink.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_log_sink.2501.0.0.dylib',
   'BINARY'),
  ('libiconv.2.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libiconv.2.dylib',
   'BINARY'),
  ('libkrb5support.1.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libkrb5support.1.1.dylib',
   'BINARY'),
  ('libcom_err.3.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libcom_err.3.0.dylib',
   'BINARY'),
  ('libk5crypto.3.1.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libk5crypto.3.1.dylib',
   'BINARY'),
  ('libkrb5.3.3.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libkrb5.3.3.dylib',
   'BINARY'),
  ('libabsl_demangle_internal.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_demangle_internal.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_demangle_rust.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_demangle_rust.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_decode_rust_punycode.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_decode_rust_punycode.2501.0.0.dylib',
   'BINARY'),
  ('libabsl_utf8_for_code_point.2501.0.0.dylib',
   '/opt/miniconda3/envs/web-bsa/lib/libabsl_utf8_for_code_point.2501.0.0.dylib',
   'BINARY'),
  ('bsa_core.py', '/Users/<USER>/Desktop/BSA-Test/bsa_core.py', 'DATA'),
  ('bsa_ui.py', '/Users/<USER>/Desktop/BSA-Test/bsa_ui.py', 'DATA'),
  ('config.json', '/Users/<USER>/Desktop/BSA-Test/config.json', 'DATA'),
  ('streamlit/runtime/__init__.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__init__.py',
   'DATA'),
  ('streamlit/runtime/__pycache__/__init__.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/app_session.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/app_session.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/connection_factory.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/connection_factory.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/context.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/context.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/context_util.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/context_util.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/credentials.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/credentials.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/forward_msg_cache.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/forward_msg_cache.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/forward_msg_queue.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/forward_msg_queue.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/fragment.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/fragment.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/media_file_manager.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/media_file_manager.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/media_file_storage.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/media_file_storage.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/memory_media_file_storage.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/memory_media_file_storage.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/memory_session_storage.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/memory_session_storage.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/memory_uploaded_file_manager.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/memory_uploaded_file_manager.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/metrics_util.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/metrics_util.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/pages_manager.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/pages_manager.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/runtime.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/runtime.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/runtime_util.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/runtime_util.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/script_data.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/script_data.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/secrets.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/secrets.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/session_manager.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/session_manager.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/stats.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/stats.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/uploaded_file_manager.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/uploaded_file_manager.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/__pycache__/websocket_session_manager.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/__pycache__/websocket_session_manager.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/app_session.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/app_session.py',
   'DATA'),
  ('streamlit/runtime/caching/__init__.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__init__.py',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/__init__.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/cache_data_api.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/cache_data_api.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/cache_errors.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/cache_errors.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/cache_resource_api.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/cache_resource_api.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/cache_type.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/cache_type.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/cache_utils.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/cache_utils.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/cached_message_replay.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/cached_message_replay.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/hashing.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/hashing.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/__pycache__/legacy_cache_api.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/__pycache__/legacy_cache_api.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/cache_data_api.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/cache_data_api.py',
   'DATA'),
  ('streamlit/runtime/caching/cache_errors.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/cache_errors.py',
   'DATA'),
  ('streamlit/runtime/caching/cache_resource_api.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/cache_resource_api.py',
   'DATA'),
  ('streamlit/runtime/caching/cache_type.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/cache_type.py',
   'DATA'),
  ('streamlit/runtime/caching/cache_utils.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/cache_utils.py',
   'DATA'),
  ('streamlit/runtime/caching/cached_message_replay.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/cached_message_replay.py',
   'DATA'),
  ('streamlit/runtime/caching/hashing.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/hashing.py',
   'DATA'),
  ('streamlit/runtime/caching/legacy_cache_api.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/legacy_cache_api.py',
   'DATA'),
  ('streamlit/runtime/caching/storage/__init__.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__init__.py',
   'DATA'),
  ('streamlit/runtime/caching/storage/__pycache__/__init__.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/storage/__pycache__/cache_storage_protocol.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__pycache__/cache_storage_protocol.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/storage/__pycache__/dummy_cache_storage.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__pycache__/dummy_cache_storage.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/storage/__pycache__/in_memory_cache_storage_wrapper.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__pycache__/in_memory_cache_storage_wrapper.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/storage/__pycache__/local_disk_cache_storage.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__pycache__/local_disk_cache_storage.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/caching/storage/cache_storage_protocol.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/cache_storage_protocol.py',
   'DATA'),
  ('streamlit/runtime/caching/storage/dummy_cache_storage.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/dummy_cache_storage.py',
   'DATA'),
  ('streamlit/runtime/caching/storage/in_memory_cache_storage_wrapper.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/in_memory_cache_storage_wrapper.py',
   'DATA'),
  ('streamlit/runtime/caching/storage/local_disk_cache_storage.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/caching/storage/local_disk_cache_storage.py',
   'DATA'),
  ('streamlit/runtime/connection_factory.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/connection_factory.py',
   'DATA'),
  ('streamlit/runtime/context.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/context.py',
   'DATA'),
  ('streamlit/runtime/context_util.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/context_util.py',
   'DATA'),
  ('streamlit/runtime/credentials.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/credentials.py',
   'DATA'),
  ('streamlit/runtime/forward_msg_cache.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/forward_msg_cache.py',
   'DATA'),
  ('streamlit/runtime/forward_msg_queue.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/forward_msg_queue.py',
   'DATA'),
  ('streamlit/runtime/fragment.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/fragment.py',
   'DATA'),
  ('streamlit/runtime/media_file_manager.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/media_file_manager.py',
   'DATA'),
  ('streamlit/runtime/media_file_storage.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/media_file_storage.py',
   'DATA'),
  ('streamlit/runtime/memory_media_file_storage.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/memory_media_file_storage.py',
   'DATA'),
  ('streamlit/runtime/memory_session_storage.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/memory_session_storage.py',
   'DATA'),
  ('streamlit/runtime/memory_uploaded_file_manager.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/memory_uploaded_file_manager.py',
   'DATA'),
  ('streamlit/runtime/metrics_util.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/metrics_util.py',
   'DATA'),
  ('streamlit/runtime/pages_manager.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/pages_manager.py',
   'DATA'),
  ('streamlit/runtime/runtime.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/runtime.py',
   'DATA'),
  ('streamlit/runtime/runtime_util.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/runtime_util.py',
   'DATA'),
  ('streamlit/runtime/script_data.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/script_data.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner/__init__.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__init__.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner/__pycache__/__init__.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner/__pycache__/exec_code.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__pycache__/exec_code.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner/__pycache__/magic.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__pycache__/magic.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner/__pycache__/magic_funcs.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__pycache__/magic_funcs.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner/__pycache__/script_cache.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__pycache__/script_cache.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner/__pycache__/script_runner.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__pycache__/script_runner.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner/exec_code.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/exec_code.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner/magic.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/magic.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner/magic_funcs.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/magic_funcs.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner/script_cache.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/script_cache.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner/script_runner.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/script_runner.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/__init__.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/__init__.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/__pycache__/__init__.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/__pycache__/exceptions.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/__pycache__/exceptions.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/__pycache__/script_requests.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/__pycache__/script_requests.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/__pycache__/script_run_context.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/__pycache__/script_run_context.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/exceptions.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/exceptions.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/script_requests.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/script_requests.py',
   'DATA'),
  ('streamlit/runtime/scriptrunner_utils/script_run_context.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/script_run_context.py',
   'DATA'),
  ('streamlit/runtime/secrets.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/secrets.py',
   'DATA'),
  ('streamlit/runtime/session_manager.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/session_manager.py',
   'DATA'),
  ('streamlit/runtime/state/__init__.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__init__.py',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/__init__.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/common.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/common.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/query_params.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/query_params.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/query_params_proxy.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/query_params_proxy.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/safe_session_state.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/safe_session_state.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/session_state.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/session_state.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/session_state_proxy.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/session_state_proxy.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/__pycache__/widgets.cpython-313.pyc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/__pycache__/widgets.cpython-313.pyc',
   'DATA'),
  ('streamlit/runtime/state/common.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/common.py',
   'DATA'),
  ('streamlit/runtime/state/query_params.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/query_params.py',
   'DATA'),
  ('streamlit/runtime/state/query_params_proxy.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/query_params_proxy.py',
   'DATA'),
  ('streamlit/runtime/state/safe_session_state.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/safe_session_state.py',
   'DATA'),
  ('streamlit/runtime/state/session_state.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/session_state.py',
   'DATA'),
  ('streamlit/runtime/state/session_state_proxy.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/session_state_proxy.py',
   'DATA'),
  ('streamlit/runtime/state/widgets.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/state/widgets.py',
   'DATA'),
  ('streamlit/runtime/stats.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/stats.py',
   'DATA'),
  ('streamlit/runtime/uploaded_file_manager.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/uploaded_file_manager.py',
   'DATA'),
  ('streamlit/runtime/websocket_session_manager.py',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/runtime/websocket_session_manager.py',
   'DATA'),
  ('streamlit/static/favicon.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/favicon.png',
   'DATA'),
  ('streamlit/static/index.html',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/index.html',
   'DATA'),
  ('streamlit/static/manifest.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/manifest.json',
   'DATA'),
  ('streamlit/static/static/css/index.C5t3M85E.css',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/css/index.C5t3M85E.css',
   'DATA'),
  ('streamlit/static/static/css/index.CJVRHjQZ.css',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/css/index.CJVRHjQZ.css',
   'DATA'),
  ('streamlit/static/static/css/index.DzuxGC_t.css',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/css/index.DzuxGC_t.css',
   'DATA'),
  ('streamlit/static/static/js/ErrorOutline.esm.6PVAQvlT.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/ErrorOutline.esm.6PVAQvlT.js',
   'DATA'),
  ('streamlit/static/static/js/FileDownload.esm.BZQHC61b.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/FileDownload.esm.BZQHC61b.js',
   'DATA'),
  ('streamlit/static/static/js/FileHelper.Bn1VShMJ.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/FileHelper.Bn1VShMJ.js',
   'DATA'),
  ('streamlit/static/static/js/FormClearHelper.CsFEiTNN.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/FormClearHelper.CsFEiTNN.js',
   'DATA'),
  ('streamlit/static/static/js/Hooks.DguOHQL1.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/Hooks.DguOHQL1.js',
   'DATA'),
  ('streamlit/static/static/js/InputInstructions.CTYn2BJQ.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/InputInstructions.CTYn2BJQ.js',
   'DATA'),
  ('streamlit/static/static/js/ProgressBar.CPOGBKCi.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/ProgressBar.CPOGBKCi.js',
   'DATA'),
  ('streamlit/static/static/js/RenderInPortalIfExists.BYu_CZaF.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/RenderInPortalIfExists.BYu_CZaF.js',
   'DATA'),
  ('streamlit/static/static/js/Toolbar.gXKw7ANv.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/Toolbar.gXKw7ANv.js',
   'DATA'),
  ('streamlit/static/static/js/UploadFileInfo.0DCkpDDf.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/UploadFileInfo.0DCkpDDf.js',
   'DATA'),
  ('streamlit/static/static/js/base-input.DBYPj91R.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/base-input.DBYPj91R.js',
   'DATA'),
  ('streamlit/static/static/js/checkbox.BUm2vnNv.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/checkbox.BUm2vnNv.js',
   'DATA'),
  ('streamlit/static/static/js/createDownloadLinkElement.DZMwyjvU.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/createDownloadLinkElement.DZMwyjvU.js',
   'DATA'),
  ('streamlit/static/static/js/createSuper.KD4RuZ-W.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/createSuper.KD4RuZ-W.js',
   'DATA'),
  ('streamlit/static/static/js/data-grid-overlay-editor.CUwpDfvI.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/data-grid-overlay-editor.CUwpDfvI.js',
   'DATA'),
  ('streamlit/static/static/js/downloader.CkDtclup.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/downloader.CkDtclup.js',
   'DATA'),
  ('streamlit/static/static/js/es6.Dlcvh_r0.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/es6.Dlcvh_r0.js',
   'DATA'),
  ('streamlit/static/static/js/iframeResizer.contentWindow.DOXlFfve.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/iframeResizer.contentWindow.DOXlFfve.js',
   'DATA'),
  ('streamlit/static/static/js/index.B0cuGMAB.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.B0cuGMAB.js',
   'DATA'),
  ('streamlit/static/static/js/index.BCWTclSV.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BCWTclSV.js',
   'DATA'),
  ('streamlit/static/static/js/index.BJY_fap7.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BJY_fap7.js',
   'DATA'),
  ('streamlit/static/static/js/index.BL3l6dnk.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BL3l6dnk.js',
   'DATA'),
  ('streamlit/static/static/js/index.BMZzRZjB.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BMZzRZjB.js',
   'DATA'),
  ('streamlit/static/static/js/index.BOzUTGDe.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BOzUTGDe.js',
   'DATA'),
  ('streamlit/static/static/js/index.BYI5iO-o.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BYI5iO-o.js',
   'DATA'),
  ('streamlit/static/static/js/index.BYo0ywlm.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BYo0ywlm.js',
   'DATA'),
  ('streamlit/static/static/js/index.BYz9btsY.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.BYz9btsY.js',
   'DATA'),
  ('streamlit/static/static/js/index.CCVzQz0Z.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CCVzQz0Z.js',
   'DATA'),
  ('streamlit/static/static/js/index.CD6FydK9.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CD6FydK9.js',
   'DATA'),
  ('streamlit/static/static/js/index.CDYEqgC8.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CDYEqgC8.js',
   'DATA'),
  ('streamlit/static/static/js/index.CMP9c4xA.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CMP9c4xA.js',
   'DATA'),
  ('streamlit/static/static/js/index.CN30QAPD.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CN30QAPD.js',
   'DATA'),
  ('streamlit/static/static/js/index.CNqWQkTe.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CNqWQkTe.js',
   'DATA'),
  ('streamlit/static/static/js/index.CaxS67Xz.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CaxS67Xz.js',
   'DATA'),
  ('streamlit/static/static/js/index.CbsT4sGW.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.CbsT4sGW.js',
   'DATA'),
  ('streamlit/static/static/js/index.ChAVlxpQ.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.ChAVlxpQ.js',
   'DATA'),
  ('streamlit/static/static/js/index.ClLMMmDd.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.ClLMMmDd.js',
   'DATA'),
  ('streamlit/static/static/js/index.D-O9rQmV.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.D-O9rQmV.js',
   'DATA'),
  ('streamlit/static/static/js/index.D4k7VZZL.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.D4k7VZZL.js',
   'DATA'),
  ('streamlit/static/static/js/index.DLBi0Ar1.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.DLBi0Ar1.js',
   'DATA'),
  ('streamlit/static/static/js/index.DVq5XmJo.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.DVq5XmJo.js',
   'DATA'),
  ('streamlit/static/static/js/index.DZKmKXWw.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.DZKmKXWw.js',
   'DATA'),
  ('streamlit/static/static/js/index.DkaVx80F.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.DkaVx80F.js',
   'DATA'),
  ('streamlit/static/static/js/index.Dr968Klx.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.Dr968Klx.js',
   'DATA'),
  ('streamlit/static/static/js/index.DtUYLn9j.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.DtUYLn9j.js',
   'DATA'),
  ('streamlit/static/static/js/index.DwjYSyhs.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.DwjYSyhs.js',
   'DATA'),
  ('streamlit/static/static/js/index.DzrImxu4.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.DzrImxu4.js',
   'DATA'),
  ('streamlit/static/static/js/index.HyGsn4VM.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.HyGsn4VM.js',
   'DATA'),
  ('streamlit/static/static/js/index.OwxC65od.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.OwxC65od.js',
   'DATA'),
  ('streamlit/static/static/js/index.PZs7VZkC.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.PZs7VZkC.js',
   'DATA'),
  ('streamlit/static/static/js/index.Voiqpj4q.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.Voiqpj4q.js',
   'DATA'),
  ('streamlit/static/static/js/index.bSROvR-J.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.bSROvR-J.js',
   'DATA'),
  ('streamlit/static/static/js/index.oT9GD3l4.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.oT9GD3l4.js',
   'DATA'),
  ('streamlit/static/static/js/index.qb-yAPH6.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.qb-yAPH6.js',
   'DATA'),
  ('streamlit/static/static/js/index.rJFy_Ygy.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/index.rJFy_Ygy.js',
   'DATA'),
  ('streamlit/static/static/js/input.CwQtEnFN.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/input.CwQtEnFN.js',
   'DATA'),
  ('streamlit/static/static/js/inputUtils.CQWz5UKz.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/inputUtils.CQWz5UKz.js',
   'DATA'),
  ('streamlit/static/static/js/memory.C5XaFIjR.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/memory.C5XaFIjR.js',
   'DATA'),
  ('streamlit/static/static/js/mergeWith.DzwwH6AG.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/mergeWith.DzwwH6AG.js',
   'DATA'),
  ('streamlit/static/static/js/number-overlay-editor.Dx0XqCkD.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/number-overlay-editor.Dx0XqCkD.js',
   'DATA'),
  ('streamlit/static/static/js/possibleConstructorReturn.CVfSu9Ws.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/possibleConstructorReturn.CVfSu9Ws.js',
   'DATA'),
  ('streamlit/static/static/js/sandbox.BT0gdMXk.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/sandbox.BT0gdMXk.js',
   'DATA'),
  ('streamlit/static/static/js/sprintf.D7DtBTRn.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/sprintf.D7DtBTRn.js',
   'DATA'),
  ('streamlit/static/static/js/textarea.DNCbrtbM.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/textarea.DNCbrtbM.js',
   'DATA'),
  ('streamlit/static/static/js/threshold.DjX0wlsa.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/threshold.DjX0wlsa.js',
   'DATA'),
  ('streamlit/static/static/js/timepicker.4UYJD9Ts.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/timepicker.4UYJD9Ts.js',
   'DATA'),
  ('streamlit/static/static/js/timer.CAwTRJ_g.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/timer.CAwTRJ_g.js',
   'DATA'),
  ('streamlit/static/static/js/toConsumableArray.DUmnaVWV.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/toConsumableArray.DUmnaVWV.js',
   'DATA'),
  ('streamlit/static/static/js/uniqueId.DUvh-GL8.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/uniqueId.DUvh-GL8.js',
   'DATA'),
  ('streamlit/static/static/js/useBasicWidgetState.Cwd7-jJa.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/useBasicWidgetState.Cwd7-jJa.js',
   'DATA'),
  ('streamlit/static/static/js/useOnInputChange.DvemQrOM.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/useOnInputChange.DvemQrOM.js',
   'DATA'),
  ('streamlit/static/static/js/value.CgPGBV_l.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/value.CgPGBV_l.js',
   'DATA'),
  ('streamlit/static/static/js/withFullScreenWrapper.CiQ10ByU.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/js/withFullScreenWrapper.CiQ10ByU.js',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_AMS-Regular.BQhdFMY1.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_AMS-Regular.BQhdFMY1.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_AMS-Regular.DMm9YOAa.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_AMS-Regular.DMm9YOAa.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_AMS-Regular.DRggAlZN.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_AMS-Regular.DRggAlZN.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Bold.ATXxdsX0.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Bold.ATXxdsX0.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Bold.BEiXGLvX.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Bold.BEiXGLvX.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Bold.Dq_IR9rO.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Bold.Dq_IR9rO.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Regular.CTRA-rTL.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Regular.CTRA-rTL.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Regular.Di6jR-x-.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Regular.Di6jR-x-.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Regular.wX97UBjC.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Regular.wX97UBjC.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Bold.BdnERNNW.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Bold.BdnERNNW.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Bold.BsDP51OF.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Bold.BsDP51OF.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Bold.CL6g_b3V.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Bold.CL6g_b3V.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Regular.CB_wures.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Regular.CB_wures.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Regular.CTYiF6lA.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Regular.CTYiF6lA.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Regular.Dxdc4cR9.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Regular.Dxdc4cR9.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Bold.Cx986IdX.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Bold.Cx986IdX.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Bold.Jm3AIy58.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Bold.Jm3AIy58.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Bold.waoOVXN0.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Bold.waoOVXN0.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-BoldItalic.DxDJ3AOS.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-BoldItalic.DxDJ3AOS.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-BoldItalic.DzxPMmG6.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-BoldItalic.DzxPMmG6.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-BoldItalic.SpSLRI95.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-BoldItalic.SpSLRI95.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Italic.3WenGoN9.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Italic.3WenGoN9.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Italic.BMLOBm91.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Italic.BMLOBm91.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Italic.NWA7e6Wa.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Italic.NWA7e6Wa.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Regular.B22Nviop.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Regular.B22Nviop.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Regular.Dr94JaBh.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Regular.Dr94JaBh.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Regular.ypZvNtVU.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Regular.ypZvNtVU.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-BoldItalic.B3XSjfu4.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-BoldItalic.B3XSjfu4.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-BoldItalic.CZnvNsCZ.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-BoldItalic.CZnvNsCZ.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-BoldItalic.iY-2wyZ7.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-BoldItalic.iY-2wyZ7.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-Italic.DA0__PXp.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-Italic.DA0__PXp.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-Italic.flOr_0UB.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-Italic.flOr_0UB.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-Italic.t53AETM-.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-Italic.t53AETM-.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Bold.CFMepnvq.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Bold.CFMepnvq.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Bold.D1sUS0GD.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Bold.D1sUS0GD.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Bold.DbIhKOiC.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Bold.DbIhKOiC.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Italic.C3H0VqGB.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Italic.C3H0VqGB.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Italic.DN2j7dab.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Italic.DN2j7dab.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Italic.YYjJ1zSn.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Italic.YYjJ1zSn.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Regular.BNo7hRIc.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Regular.BNo7hRIc.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Regular.CS6fqUqJ.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Regular.CS6fqUqJ.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Regular.DDBCnlJ7.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Regular.DDBCnlJ7.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Script-Regular.C5JkGWo-.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Script-Regular.C5JkGWo-.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Script-Regular.D3wIWfF6.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Script-Regular.D3wIWfF6.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Script-Regular.D5yQViql.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Script-Regular.D5yQViql.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size1-Regular.C195tn64.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size1-Regular.C195tn64.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size1-Regular.Dbsnue_I.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size1-Regular.Dbsnue_I.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size1-Regular.mCD8mA8B.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size1-Regular.mCD8mA8B.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size2-Regular.B7gKUWhC.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size2-Regular.B7gKUWhC.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size2-Regular.Dy4dx90m.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size2-Regular.Dy4dx90m.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size2-Regular.oD1tc_U0.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size2-Regular.oD1tc_U0.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size3-Regular.CTq5MqoE.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size3-Regular.CTq5MqoE.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size3-Regular.DgpXs0kz.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size3-Regular.DgpXs0kz.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size4-Regular.BF-4gkZK.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size4-Regular.BF-4gkZK.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size4-Regular.DWFBv043.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size4-Regular.DWFBv043.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size4-Regular.Dl5lxZxV.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size4-Regular.Dl5lxZxV.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Typewriter-Regular.C0xS9mPB.woff',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Typewriter-Regular.C0xS9mPB.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Typewriter-Regular.CO6r4hn1.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Typewriter-Regular.CO6r4hn1.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Typewriter-Regular.D3Ib7_Hf.ttf',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Typewriter-Regular.D3Ib7_Hf.ttf',
   'DATA'),
  ('streamlit/static/static/media/MaterialSymbols-Rounded.DsbC8sYI.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/MaterialSymbols-Rounded.DsbC8sYI.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceCodeVF-Italic.ttf.Ba1oaZG1.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/SourceCodeVF-Italic.ttf.Ba1oaZG1.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceCodeVF-Upright.ttf.BjWn63N-.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/SourceCodeVF-Upright.ttf.BjWn63N-.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSansVF-Italic.ttf.Bt9VkdQ3.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/SourceSansVF-Italic.ttf.Bt9VkdQ3.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSansVF-Upright.ttf.BsWL4Kly.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/SourceSansVF-Upright.ttf.BsWL4Kly.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSerifVariable-Italic.ttf.CVdzAtxO.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/SourceSerifVariable-Italic.ttf.CVdzAtxO.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSerifVariable-Roman.ttf.mdpVL9bi.woff2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/SourceSerifVariable-Roman.ttf.mdpVL9bi.woff2',
   'DATA'),
  ('streamlit/static/static/media/balloon-0.Czj7AKwE.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/balloon-0.Czj7AKwE.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-1.CNvFFrND.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/balloon-1.CNvFFrND.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-2.DTvC6B1t.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/balloon-2.DTvC6B1t.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-3.CgSk4tbL.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/balloon-3.CgSk4tbL.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-4.mbtFrzxf.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/balloon-4.mbtFrzxf.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-5.CSwkUfRA.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/balloon-5.CSwkUfRA.png',
   'DATA'),
  ('streamlit/static/static/media/fireworks.B4d-_KUe.gif',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/fireworks.B4d-_KUe.gif',
   'DATA'),
  ('streamlit/static/static/media/flake-0.DgWaVvm5.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/flake-0.DgWaVvm5.png',
   'DATA'),
  ('streamlit/static/static/media/flake-1.B2r5AHMK.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/flake-1.B2r5AHMK.png',
   'DATA'),
  ('streamlit/static/static/media/flake-2.BnWSExPC.png',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/flake-2.BnWSExPC.png',
   'DATA'),
  ('streamlit/static/static/media/snowflake.JU2jBHL8.svg',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/static/static/media/snowflake.JU2jBHL8.svg',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('lz4-4.4.4.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4-4.4.4.dist-info/METADATA',
   'DATA'),
  ('lz4-4.4.4.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4-4.4.4.dist-info/WHEEL',
   'DATA'),
  ('lz4-4.4.4.dist-info/licenses/LICENSE',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4-4.4.4.dist-info/licenses/LICENSE',
   'DATA'),
  ('lz4-4.4.4.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4-4.4.4.dist-info/RECORD',
   'DATA'),
  ('lz4-4.4.4.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4-4.4.4.dist-info/INSTALLER',
   'DATA'),
  ('lz4-4.4.4.dist-info/top_level.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/lz4-4.4.4.dist-info/top_level.txt',
   'DATA'),
  ('certifi/cacert.pem',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/certifi/py.typed',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/chunked_array.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/chunked_array.h',
   'DATA'),
  ('pyarrow/lib.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/lib.pxd',
   'DATA'),
  ('pyarrow/include/arrow/testing/math.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/math.h',
   'DATA'),
  ('pyarrow/include/arrow/util/thread_pool.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/thread_pool.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/exec.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/exec.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/test_nodes.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/test_nodes.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_python_internal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_compat.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/array.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/server_session_middleware.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/server_session_middleware.h',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow/include/arrow/testing/future_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/future_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   'DATA'),
  ('pyarrow/include/arrow/json/chunker.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunker.h',
   'DATA'),
  ('pyarrow/include/arrow/util/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/api.h',
   'DATA'),
  ('pyarrow/include/arrow/type.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/type.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   'DATA'),
  ('pyarrow/include/arrow/util/compare.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/compare.h',
   'DATA'),
  ('pyarrow/includes/libarrow_fs.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_fs.pxd',
   'DATA'),
  ('pyarrow/include/arrow/acero/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/server_session_middleware_factory.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/server_session_middleware_factory.h',
   'DATA'),
  ('pyarrow/include/arrow/util/base64.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/base64.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/bloom_filter.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/bloom_filter.h',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_definitions.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_definitions.h',
   'DATA'),
  ('pyarrow/_hdfs.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_hdfs.pyx',
   'DATA'),
  ('pyarrow/pandas-shim.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/pandas-shim.pxi',
   'DATA'),
  ('pyarrow/device.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/device.pxi',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_interop.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_middleware.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_middleware.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.cc',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/api.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/acero/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/pch.h',
   'DATA'),
  ('pyarrow/include/arrow/c/helpers.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/c/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/random.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/random.h',
   'DATA'),
  ('pyarrow/_parquet_encryption.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_vector.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_vector.h',
   'DATA'),
  ('pyarrow/_compute.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_compute.pyx',
   'DATA'),
  ('pyarrow/include/arrow/ipc/dictionary.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/dictionary.h',
   'DATA'),
  ('pyarrow/include/arrow/util/test_common.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/test_common.h',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/_cuda.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/io/interfaces.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/interfaces.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_scalar_inline.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/visit_scalar_inline.h',
   'DATA'),
  ('pyarrow/_fs.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_fs.pxd',
   'DATA'),
  ('pyarrow/include/arrow/json/test_common.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/builder.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_adaptive.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_adaptive.h',
   'DATA'),
  ('pyarrow/include/arrow/telemetry/logging.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/telemetry/logging.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/options.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/options.h',
   'DATA'),
  ('pyarrow/include/arrow/python/python_test.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/schema_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/schema_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/key_value_metadata.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/key_value_metadata.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/fixed_width_test_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/fixed_width_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_lib.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitset_stack.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitset_stack.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_auth.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_auth.h',
   'DATA'),
  ('pyarrow/include/arrow/util/hash_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/hash_util.h',
   'DATA'),
  ('pyarrow/include/arrow/io/buffered.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/buffered.h',
   'DATA'),
  ('pyarrow/includes/libarrow_substrait.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow/include/arrow/dataset/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/visibility.h',
   'DATA'),
  ('pyarrow/types.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/types.pxi',
   'DATA'),
  ('pyarrow/lib_api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/lib_api.h',
   'DATA'),
  ('pyarrow/include/arrow/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/accumulation_queue.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/accumulation_queue.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset_writer.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/ios.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/ios.h',
   'DATA'),
  ('pyarrow/include/arrow/config.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/config.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/crc32.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/crc32.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.cc',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/localfs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/localfs.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/adapter.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/adapter.h',
   'DATA'),
  ('pyarrow/include/arrow/util/string_builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/string_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/test_common.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/test_common.h',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/include/arrow/util/ree_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/ree_util.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_to_arrow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/io/compressed.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/compressed.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/utils.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/utils.h',
   'DATA'),
  ('pyarrow/include/arrow/python/benchmark.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/include/arrow/util/functional.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/functional.h',
   'DATA'),
  ('pyarrow/include/arrow/util/future.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/future.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_primitive.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_primitive.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/path_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/path_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/map.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/map.h',
   'DATA'),
  ('pyarrow/include/arrow/util/trie.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/trie.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_json.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_json.h',
   'DATA'),
  ('pyarrow/include/arrow/type_traits.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/uuid.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/extension/uuid.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bpacking_avx512.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bpacking_avx512.h',
   'DATA'),
  ('pyarrow/include/arrow/json/chunked_builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunked_builder.h',
   'DATA'),
  ('pyarrow/_dataset.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset.pyx',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_decimal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/compare.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compare.h',
   'DATA'),
  ('pyarrow/_cuda.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_cuda.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/string.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/string.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server.h',
   'DATA'),
  ('pyarrow/include/arrow/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/api.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/column_metadata.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/column_metadata.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_util.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport_server.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport_server.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/api.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/strptime.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/strptime.h',
   'DATA'),
  ('pyarrow/include/arrow/json/object_writer.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/checked_cast.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/checked_cast.h',
   'DATA'),
  ('pyarrow/gandiva.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/gandiva.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_types.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_types.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/server.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/server.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/projector.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/projector.h',
   'DATA'),
  ('pyarrow/include/arrow/io/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bpacking_avx2.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bpacking_avx2.h',
   'DATA'),
  ('pyarrow/lib.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/lib.h',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/iterators.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/include/arrow/python/csv.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_internal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_internal.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   'DATA'),
  ('pyarrow/include/arrow/io/file.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/file.h',
   'DATA'),
  ('pyarrow/include/arrow/array/data.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/data.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/feather.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/feather.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/query_context.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/query_context.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack_abi.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack_abi.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_run_end.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_run_end.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_block_counter.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_block_counter.h',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_set.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_set.h',
   'DATA'),
  ('pyarrow/include/arrow/util/algorithm.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/algorithm.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/serde.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/serde.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/cast.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/cast.h',
   'DATA'),
  ('pyarrow/include/arrow/python/arrow_to_pandas.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/benchmark.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/benchmark.pxi',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_ipc.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_ipc.h',
   'DATA'),
  ('pyarrow/includes/common.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/common.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_writer.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/partition_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/partition_util.h',
   'DATA'),
  ('pyarrow/include/arrow/pretty_print.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/pretty_print.h',
   'DATA'),
  ('pyarrow/_dataset_orc.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset_orc.pyx',
   'DATA'),
  ('pyarrow/include/arrow/stl_allocator.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/stl_allocator.h',
   'DATA'),
  ('pyarrow/public-api.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/public-api.pxi',
   'DATA'),
  ('pyarrow/include/arrow/json/options.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/options.h',
   'DATA'),
  ('pyarrow/include/arrow/c/bridge.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/c/bridge.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/registry.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/registry.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_util.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_aggregate.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_aggregate.h',
   'DATA'),
  ('pyarrow/includes/libarrow_flight.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_flight.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/datum.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/datum.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem_library.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem_library.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/_flight.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_flight.pyx',
   'DATA'),
  ('pyarrow/include/arrow/visit_array_inline.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/visit_array_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/api.h',
   'DATA'),
  ('pyarrow/include/arrow/python/parquet_encryption.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_ops.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_ops.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_tracing_middleware.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_tracing_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_run_reader.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_run_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/table_builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/table_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/util/union_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/union_util.h',
   'DATA'),
  ('pyarrow/includes/libgandiva.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libgandiva.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/macros.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/macros.h',
   'DATA'),
  ('pyarrow/include/arrow/util/decimal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/python/platform.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/io/concurrency.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/concurrency.h',
   'DATA'),
  ('pyarrow/lib.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/lib.pyx',
   'DATA'),
  ('pyarrow/include/arrow/dataset/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/type_fwd.h',
   'DATA'),
  ('pyarrow/_compute.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_compute.pxd',
   'DATA'),
  ('pyarrow/compat.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/compat.pxi',
   'DATA'),
  ('pyarrow/_azurefs.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_azurefs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_base.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_base.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util.h',
   'DATA'),
  ('pyarrow/include/arrow/io/slow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/slow.h',
   'DATA'),
  ('pyarrow/include/arrow/python/inference.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/_acero.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_acero.pyx',
   'DATA'),
  ('pyarrow/include/arrow/visitor_generate.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/visitor_generate.h',
   'DATA'),
  ('pyarrow/include/arrow/io/mman.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/mman.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/otel_logging.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/otel_logging.h',
   'DATA'),
  ('pyarrow/__init__.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/__init__.pxd',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_union.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_union.h',
   'DATA'),
  ('pyarrow/include/arrow/util/double_conversion.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/double_conversion.h',
   'DATA'),
  ('pyarrow/include/arrow/util/cpu_info.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/cpu_info.h',
   'DATA'),
  ('pyarrow/include/arrow/device_allocation_type_set.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/device_allocation_type_set.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/expression.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/expression.h',
   'DATA'),
  ('pyarrow/include/arrow/io/memory.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/memory.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_lib.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_csv.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_csv.h',
   'DATA'),
  ('pyarrow/include/arrow/python/datetime.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/partition.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/partition.h',
   'DATA'),
  ('pyarrow/include/arrow/util/converter.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/visitor.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/visitor.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   'DATA'),
  ('pyarrow/include/arrow/util/endian.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/endian.h',
   'DATA'),
  ('pyarrow/include/arrow/util/hashing.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/hashing.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bpacking_neon.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bpacking_neon.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/row/grouper.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/row/grouper.h',
   'DATA'),
  ('pyarrow/include/arrow/integration/json_integration.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/integration/json_integration.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/dict_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/dict_util.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset_parquet.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/task_group.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/task_group.h',
   'DATA'),
  ('pyarrow/include/arrow/util/dispatch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/dispatch.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz_private.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz_private.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_binary.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_binary.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/azurefs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/azurefs.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/api.h',
   'DATA'),
  ('pyarrow/includes/libarrow_feather.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_feather.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/CMakeLists.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/arrow/ipc/json_simple.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/json_simple.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_cookie_middleware.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_cookie_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/array/validate.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/validate.h',
   'DATA'),
  ('pyarrow/include/arrow/util/small_vector.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/small_vector.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/hdfs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/hdfs.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api.h',
   'DATA'),
  ('pyarrow/include/arrow/json/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/api.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/type_fwd.h',
   'DATA'),
  ('pyarrow/_gcsfs.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_gcsfs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/visit_data_inline.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/visit_data_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bpacking64_default.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bpacking64_default.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/reader.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/reader.h',
   'DATA'),
  ('pyarrow/_dataset_parquet.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/ipc/test_common.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/api.h',
   'DATA'),
  ('pyarrow/ipc.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/ipc.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/prefetch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/prefetch.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset.h',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/logger.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/logger.h',
   'DATA'),
  ('pyarrow/builder.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/builder.pxi',
   'DATA'),
  ('pyarrow/include/arrow/ipc/message.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/message.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/extension_type.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/extension_type.h',
   'DATA'),
  ('pyarrow/includes/libarrow.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/tracing.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/tracing.h',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.cc',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_middleware.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/uniform_real.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/uniform_real.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_nested.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_nested.h',
   'DATA'),
  ('pyarrow/src/arrow/python/common.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.cc',
   'DATA'),
  ('pyarrow/include/arrow/python/udf.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/include/arrow/util/debug.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/debug.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/types.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/mockfs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/mockfs.h',
   'DATA'),
  ('pyarrow/scalar.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/scalar.pxi',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/scanner.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/scanner.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/executor_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/executor_util.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_tracing_middleware.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_tracing_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/decimal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/opaque.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/extension/opaque.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/parallel.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/parallel.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/backpressure_handler.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/backpressure_handler.h',
   'DATA'),
  ('pyarrow/src/arrow/python/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/api.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function.h',
   'DATA'),
  ('pyarrow/include/arrow/util/regex.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/regex.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_run_end.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_run_end.h',
   'DATA'),
  ('pyarrow/_dataset_parquet_encryption.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/vector.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/vector.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/exec_plan.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/exec_plan.h',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.cc',
   'DATA'),
  ('pyarrow/include/arrow/dataset/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/pch.h',
   'DATA'),
  ('pyarrow/include/arrow/io/caching.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/caching.h',
   'DATA'),
  ('pyarrow/include/arrow/util/pcg_random.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/pcg_random.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   'DATA'),
  ('pyarrow/include/arrow/tensor/converter.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/tensor/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/util/counting_semaphore.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/counting_semaphore.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/types_async.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types_async.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/tpch_node.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/tpch_node.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/discovery.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/discovery.h',
   'DATA'),
  ('pyarrow/include/arrow/python/python_to_arrow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/_substrait.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_substrait.pyx',
   'DATA'),
  ('pyarrow/array.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/array.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/mutex.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/mutex.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/options.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/options.h',
   'DATA'),
  ('pyarrow/include/arrow/array/util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/float16.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/float16.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_builders.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_builders.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/kernel.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/kernel.h',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool_test.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool_test.h',
   'DATA'),
  ('pyarrow/include/arrow/c/abi.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/c/abi.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/invalid_row.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/invalid_row.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/time_series_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/time_series_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join.h',
   'DATA'),
  ('pyarrow/include/arrow/util/utf8.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/utf8.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_primitive.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_primitive.h',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.cc',
   'DATA'),
  ('pyarrow/include/arrow/compute/util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/type_traits.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/async_test_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/async_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/record_batch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/record_batch.h',
   'DATA'),
  ('pyarrow/include/arrow/sparse_tensor.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/sparse_tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_visit.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_visit.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/date.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/date.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/types.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/types.h',
   'DATA'),
  ('pyarrow/includes/libparquet_encryption.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow/includes/libarrow_acero.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_acero.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/bpacking_default.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bpacking_default.h',
   'DATA'),
  ('pyarrow/io.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/io.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/config.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/config.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/map_node.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/map_node.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_time.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_time.h',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/includes/libarrow_cuda.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/visit_type_inline.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/visit_type_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/util/basic_decimal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/basic_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/builder.h',
   'DATA'),
  ('pyarrow/include/arrow/util/iterator.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/iterator.h',
   'DATA'),
  ('pyarrow/include/arrow/python/ipc.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/util/byte_size.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/byte_size.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   'DATA'),
  ('pyarrow/include/arrow/python/iterators.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/include/arrow/python/common.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/common.h',
   'DATA'),
  ('pyarrow/include/arrow/python/lib_api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib_api.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   'DATA'),
  ('pyarrow/include/arrow/python/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/pch.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/type_fwd.h',
   'DATA'),
  ('pyarrow/_parquet.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/stopwatch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/stopwatch.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport.h',
   'DATA'),
  ('pyarrow/include/arrow/util/unreachable.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/unreachable.h',
   'DATA'),
  ('pyarrow/includes/__init__.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/__init__.pxd',
   'DATA'),
  ('pyarrow/_dlpack.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dlpack.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/formatting.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/formatting.h',
   'DATA'),
  ('pyarrow/include/arrow/python/async.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/async.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/util/type_traits.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.cc',
   'DATA'),
  ('pyarrow/table.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/table.pxi',
   'DATA'),
  ('pyarrow/include/arrow/flight/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/api.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/aggregate_node.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/aggregate_node.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/pch.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/api.h',
   'DATA'),
  ('pyarrow/include/arrow/io/test_common.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/test_common.h',
   'DATA'),
  ('pyarrow/src/arrow/python/common.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_generate.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_generate.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/platform.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_decimal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_decimal.h',
   'DATA'),
  ('pyarrow/tensor.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/tensor.pxi',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/include/arrow/io/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/span.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/span.h',
   'DATA'),
  ('pyarrow/include/arrow/json/parser.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/parser.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_interop.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/_csv.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_csv.pyx',
   'DATA'),
  ('pyarrow/_parquet.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_nested.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_nested.h',
   'DATA'),
  ('pyarrow/include/arrow/util/ubsan.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/ubsan.h',
   'DATA'),
  ('pyarrow/_acero.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_acero.pxd',
   'DATA'),
  ('pyarrow/include/arrow/ipc/util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/util.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/options.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/options.h',
   'DATA'),
  ('pyarrow/include/arrow/util/simd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/simd.h',
   'DATA'),
  ('pyarrow/include/arrow/python/flight.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/include/arrow/extension_type.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/extension_type.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/launder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/launder.h',
   'DATA'),
  ('pyarrow/_feather.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_feather.pyx',
   'DATA'),
  ('pyarrow/include/arrow/engine/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/pch.h',
   'DATA'),
  ('pyarrow/memory.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/memory.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/list_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/list_util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_node.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_node.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/middleware.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/util/delimiting.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/delimiting.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_fixup.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_fixup.h',
   'DATA'),
  ('pyarrow/include/arrow/python/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/sort.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/sort.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/pch.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_auth_handlers.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_auth_handlers.h',
   'DATA'),
  ('pyarrow/_dataset.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset.pxd',
   'DATA'),
  ('pyarrow/include/arrow/testing/process.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/process.h',
   'DATA'),
  ('pyarrow/include/arrow/util/queue.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/queue.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/matchers.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/matchers.h',
   'DATA'),
  ('pyarrow/include/arrow/device.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/device.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_orc.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_orc.h',
   'DATA'),
  ('pyarrow/include/arrow/util/logging.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/logging.h',
   'DATA'),
  ('pyarrow/include/arrow/io/hdfs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/hdfs.h',
   'DATA'),
  ('pyarrow/include/arrow/stl_iterator.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/stl_iterator.h',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow/include/arrow/python/extension_type.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/reader.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/util/math_constants.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/math_constants.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/relation.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/relation.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/order_by_impl.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/order_by_impl.h',
   'DATA'),
  ('pyarrow/include/arrow/chunk_resolver.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/chunk_resolver.h',
   'DATA'),
  ('pyarrow/src/arrow/python/async.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/async.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/python/helpers.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/json/reader.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/array/statistics.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/statistics.h',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/include/arrow/util/rows_to_batches.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/rows_to_batches.h',
   'DATA'),
  ('pyarrow/include/arrow/util/align_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/align_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/generator.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/generator.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/_orc.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_orc.pyx',
   'DATA'),
  ('pyarrow/include/arrow/io/transform.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/transform.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_convert.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/writer.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_binary.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_binary.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/ordering.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/ordering.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/plan.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/plan.h',
   'DATA'),
  ('pyarrow/include/arrow/util/range.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/range.h',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/src/arrow/python/type_traits.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/result.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/result.h',
   'DATA'),
  ('pyarrow/include/arrow/util/print.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/print.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/asof_join_node.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/asof_join_node.h',
   'DATA'),
  ('pyarrow/_dataset_parquet.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/array/concatenate.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/concatenate.h',
   'DATA'),
  ('pyarrow/include/arrow/buffer_builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/buffer_builder.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/cancel.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/cancel.h',
   'DATA'),
  ('pyarrow/include/arrow/util/io_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/io_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/tdigest.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/tdigest.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/task_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/task_util.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/converter.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/chunker.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/chunker.h',
   'DATA'),
  ('pyarrow/_parquet_encryption.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow/include/arrow/python/filesystem.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/status.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/status.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_base.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_base.h',
   'DATA'),
  ('pyarrow/include/arrow/json/rapidjson_defs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/rapidjson_defs.h',
   'DATA'),
  ('pyarrow/_s3fs.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_s3fs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/array/array_base.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_base.h',
   'DATA'),
  ('pyarrow/include/arrow/stl.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/stl.h',
   'DATA'),
  ('pyarrow/include/arrow/util/binary_view_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/binary_view_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_flight_server.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_flight_server.h',
   'DATA'),
  ('pyarrow/include/arrow/python/io.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/io.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/parser.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/parser.h',
   'DATA'),
  ('pyarrow/_json.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_json.pyx',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/visibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/memory.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/memory.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/src/arrow/python/platform.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_decoder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_decoder.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/testing/util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_parquet.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_parquet.h',
   'DATA'),
  ('pyarrow/_orc.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_orc.pxd',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3fs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3fs.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   'DATA'),
  ('pyarrow/include/arrow/json/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/options.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/options.h',
   'DATA'),
  ('pyarrow/include/arrow/util/spaced.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/spaced.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_compatibility.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_compatibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/value_parsing.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/value_parsing.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function_options.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function_options.h',
   'DATA'),
  ('pyarrow/include/arrow/util/concurrent_map.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/concurrent_map.h',
   'DATA'),
  ('pyarrow/include/arrow/scalar.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/scalar.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_auth.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_auth.h',
   'DATA'),
  ('pyarrow/include/arrow/util/aligned_storage.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/aligned_storage.h',
   'DATA'),
  ('pyarrow/include/arrow/python/lib.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib.h',
   'DATA'),
  ('pyarrow/include/arrow/json/object_parser.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_parser.h',
   'DATA'),
  ('pyarrow/_fs.pyx',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_fs.pyx',
   'DATA'),
  ('pyarrow/includes/libarrow_python.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_python.pxd',
   'DATA'),
  ('pyarrow/include/arrow/flight/sql/client.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/sql/client.h',
   'DATA'),
  ('pyarrow/config.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/config.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/benchmark_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/benchmark_util.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/json.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/extension/json.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_dict.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_dict.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_dict.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_dict.h',
   'DATA'),
  ('pyarrow/include/arrow/python/api.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_dict.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_dict.h',
   'DATA'),
  ('pyarrow/include/arrow/json/converter.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/json/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/python/gdb.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/flight/pch.h',
   'DATA'),
  ('pyarrow/include/arrow/tensor.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_scalar.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_scalar.h',
   'DATA'),
  ('pyarrow/include/arrow/buffer.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/buffer.h',
   'DATA'),
  ('pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/util/uri.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/uri.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_reader.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/table.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/table.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow/include/arrow/io/stdio.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/io/stdio.h',
   'DATA'),
  ('pyarrow/include/arrow/util/time.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/time.h',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/type_fwd.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/type_fwd.h',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/compression.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/compression.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash.h',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util_overflow.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util_overflow.h',
   'DATA'),
  ('pyarrow/error.pxi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/error.pxi',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3_test_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/pch.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/pch.h',
   'DATA'),
  ('pyarrow/_json.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_json.pxd',
   'DATA'),
  ('pyarrow/include/arrow/acero/benchmark_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/acero/benchmark_util.h',
   'DATA'),
  ('pyarrow/_csv.pxd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/_csv.pxd',
   'DATA'),
  ('pyarrow/include/arrow/array/diff.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/array/diff.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/gcsfs.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/gcsfs.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/options.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/options.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.cc',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.cc',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/test_util.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/writer.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/csv/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/bool8.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/extension/bool8.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bpacking.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/util/bpacking.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_init.h',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_init.h',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('altair/jupyter/js/README.md',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/altair/jupyter/js/README.md',
   'DATA'),
  ('altair/py.typed',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/altair/py.typed',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-lite-schema.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-lite-schema.json',
   'DATA'),
  ('altair/jupyter/js/index.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/altair/jupyter/js/index.js',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-themes.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-themes.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/core',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/content',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/core',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/content',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/metaschema.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft7/metaschema.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft7/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft6/metaschema.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft6/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   'DATA'),
  ('jsonschema_specifications/schemas/draft3/metaschema.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft3/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/metaschema.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft4/metaschema.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft4/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/direct_url.json',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/INSTALLER',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/entry_points.txt',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/licenses/COPYING',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/licenses/COPYING',
   'DATA'),
  ('jsonschema/benchmarks/issue232/issue.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema/benchmarks/issue232/issue.json',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/RECORD',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/REQUESTED',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/METADATA',
   'DATA'),
  ('jsonschema-4.24.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/jsonschema-4.24.0.dist-info/WHEEL',
   'DATA'),
  ('plotly/package_data/templates/simple_white.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/simple_white.json',
   'DATA'),
  ('plotly/package_data/datasets/iris.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/iris.csv.gz',
   'DATA'),
  ('plotly/package_data/templates/plotly.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/plotly.json',
   'DATA'),
  ('plotly/package_data/plotly.min.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/plotly.min.js',
   'DATA'),
  ('plotly/package_data/datasets/stocks.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/stocks.csv.gz',
   'DATA'),
  ('plotly/package_data/datasets/election.geojson.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/election.geojson.gz',
   'DATA'),
  ('plotly/package_data/datasets/gapminder.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/gapminder.csv.gz',
   'DATA'),
  ('plotly/package_data/datasets/tips.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/tips.csv.gz',
   'DATA'),
  ('plotly/package_data/templates/seaborn.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/seaborn.json',
   'DATA'),
  ('plotly/package_data/templates/plotly_dark.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/plotly_dark.json',
   'DATA'),
  ('plotly/package_data/datasets/medals.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/medals.csv.gz',
   'DATA'),
  ('plotly/package_data/templates/ygridoff.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/ygridoff.json',
   'DATA'),
  ('plotly/package_data/templates/xgridoff.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/xgridoff.json',
   'DATA'),
  ('plotly/package_data/widgetbundle.js',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/widgetbundle.js',
   'DATA'),
  ('plotly/package_data/templates/ggplot2.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/ggplot2.json',
   'DATA'),
  ('plotly/package_data/datasets/experiment.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/experiment.csv.gz',
   'DATA'),
  ('plotly/package_data/templates/presentation.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/presentation.json',
   'DATA'),
  ('plotly/package_data/templates/gridon.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/gridon.json',
   'DATA'),
  ('plotly/package_data/templates/plotly_white.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/templates/plotly_white.json',
   'DATA'),
  ('plotly/package_data/datasets/carshare.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/carshare.csv.gz',
   'DATA'),
  ('plotly/package_data/datasets/election.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/election.csv.gz',
   'DATA'),
  ('plotly/package_data/datasets/wind.csv.gz',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly/package_data/datasets/wind.csv.gz',
   'DATA'),
  ('streamlit/proto/ArrowNamedDataSet_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ArrowNamedDataSet_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Radio_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Radio_pb2.pyi',
   'DATA'),
  ('streamlit/proto/TimeInput_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/TimeInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Text_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Text_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DateInput_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/DateInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/BackMsg_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/BackMsg_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AppPage_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/AppPage_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Snow_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Snow_pb2.pyi',
   'DATA'),
  ('streamlit-1.46.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/WHEEL',
   'DATA'),
  ('streamlit/proto/Spinner_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Spinner_pb2.pyi',
   'DATA'),
  ('streamlit/proto/MultiSelect_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/MultiSelect_pb2.pyi',
   'DATA'),
  ('streamlit/proto/WidgetStates_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/WidgetStates_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageLink_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/PageLink_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageInfo_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/PageInfo_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ArrowVegaLiteChart_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ArrowVegaLiteChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Metric_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Metric_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Audio_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Audio_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Delta_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Delta_pb2.pyi',
   'DATA'),
  ('streamlit/proto/GraphVizChart_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/GraphVizChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Empty_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Empty_pb2.pyi',
   'DATA'),
  ('streamlit-1.46.0.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/REQUESTED',
   'DATA'),
  ('streamlit/proto/PlotlyChart_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/PlotlyChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/TextArea_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/TextArea_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DocString_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/DocString_pb2.pyi',
   'DATA'),
  ('streamlit-1.46.0.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/direct_url.json',
   'DATA'),
  ('streamlit-1.46.0.dist-info/top_level.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/top_level.txt',
   'DATA'),
  ('streamlit-1.46.0.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/RECORD',
   'DATA'),
  ('streamlit/proto/GapSize_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/GapSize_pb2.pyi',
   'DATA'),
  ('streamlit/proto/HeightConfig_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/HeightConfig_pb2.pyi',
   'DATA'),
  ('streamlit/proto/WidthConfig_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/WidthConfig_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ColorPicker_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ColorPicker_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Favicon_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Favicon_pb2.pyi',
   'DATA'),
  ('streamlit/proto/LabelVisibilityMessage_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/LabelVisibilityMessage_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Code_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Code_pb2.pyi',
   'DATA'),
  ('streamlit-1.46.0.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/entry_points.txt',
   'DATA'),
  ('streamlit-1.46.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/INSTALLER',
   'DATA'),
  ('streamlit/proto/ButtonGroup_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ButtonGroup_pb2.pyi',
   'DATA'),
  ('streamlit/proto/LinkButton_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/LinkButton_pb2.pyi',
   'DATA'),
  ('streamlit/proto/FileUploader_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/FileUploader_pb2.pyi',
   'DATA'),
  ('streamlit/proto/openmetrics_data_model_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/openmetrics_data_model_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Image_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Image_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ClientState_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ClientState_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Exception_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Exception_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Element_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Element_pb2.pyi',
   'DATA'),
  ('streamlit/proto/NewSession_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/NewSession_pb2.pyi',
   'DATA'),
  ('streamlit/proto/VegaLiteChart_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/VegaLiteChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageProfile_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/PageProfile_pb2.pyi',
   'DATA'),
  ('streamlit/py.typed',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/py.typed',
   'DATA'),
  ('streamlit/proto/ChatInput_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ChatInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Heading_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Heading_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Arrow_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Arrow_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Logo_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Logo_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Balloons_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Balloons_pb2.pyi',
   'DATA'),
  ('streamlit/proto/NamedDataSet_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/NamedDataSet_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Common_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Common_pb2.pyi',
   'DATA'),
  ('streamlit/proto/GitInfo_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/GitInfo_pb2.pyi',
   'DATA'),
  ('streamlit/proto/SessionStatus_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/SessionStatus_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Toast_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Toast_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Progress_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Progress_pb2.pyi',
   'DATA'),
  ('streamlit/proto/CameraInput_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/CameraInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/BokehChart_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/BokehChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AutoRerun_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/AutoRerun_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Html_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Html_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Alert_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Alert_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DownloadButton_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/DownloadButton_pb2.pyi',
   'DATA'),
  ('streamlit-1.46.0.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit-1.46.0.dist-info/METADATA',
   'DATA'),
  ('streamlit/proto/MetricsEvent_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/MetricsEvent_pb2.pyi',
   'DATA'),
  ('streamlit/proto/TextInput_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/TextInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DataFrame_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/DataFrame_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Video_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Video_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AuthRedirect_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/AuthRedirect_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Block_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Block_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AudioInput_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/AudioInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Selectbox_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Selectbox_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Markdown_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Markdown_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Checkbox_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Checkbox_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ForwardMsg_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ForwardMsg_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PagesChanged_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/PagesChanged_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Skeleton_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Skeleton_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Slider_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Slider_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ParentMessage_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/ParentMessage_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageConfig_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/PageConfig_pb2.pyi',
   'DATA'),
  ('streamlit/proto/RootContainer_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/RootContainer_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageNotFound_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/PageNotFound_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Components_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Components_pb2.pyi',
   'DATA'),
  ('streamlit/proto/SessionEvent_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/SessionEvent_pb2.pyi',
   'DATA'),
  ('streamlit/proto/IFrame_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/IFrame_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Json_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Json_pb2.pyi',
   'DATA'),
  ('streamlit/proto/NumberInput_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/NumberInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Navigation_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Navigation_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DeckGlJsonChart_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/DeckGlJsonChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Button_pb2.pyi',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/streamlit/proto/Button_pb2.pyi',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('plotly-6.1.2.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/METADATA',
   'DATA'),
  ('plotly-6.1.2.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/direct_url.json',
   'DATA'),
  ('click-8.2.1.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/click-8.2.1.dist-info/REQUESTED',
   'DATA'),
  ('numpy-2.3.0.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/entry_points.txt',
   'DATA'),
  ('h2-4.2.0.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/direct_url.json',
   'DATA'),
  ('attrs-25.3.0.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/attrs-25.3.0.dist-info/direct_url.json',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('plotly-6.1.2.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/entry_points.txt',
   'DATA'),
  ('h2-4.2.0.dist-info/top_level.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/top_level.txt',
   'DATA'),
  ('plotly-6.1.2.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('numpy-2.3.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/WHEEL',
   'DATA'),
  ('numpy-2.3.0.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/attrs-25.3.0.dist-info/REQUESTED',
   'DATA'),
  ('plotly-6.1.2.dist-info/top_level.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/top_level.txt',
   'DATA'),
  ('plotly-6.1.2.dist-info/licenses/LICENSE.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info/licenses/LICENSE',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/attrs-25.3.0.dist-info/licenses/LICENSE',
   'DATA'),
  ('h2-4.2.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('h2-4.2.0.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/REQUESTED',
   'DATA'),
  ('plotly-6.1.2.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/RECORD',
   'DATA'),
  ('click-8.2.1.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/click-8.2.1.dist-info/direct_url.json',
   'DATA'),
  ('h2-4.2.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('numpy-2.3.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/attrs-25.3.0.dist-info/RECORD',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('h2-4.2.0.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/RECORD',
   'DATA'),
  ('numpy-2.3.0.dist-info/LICENSE.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/LICENSE.txt',
   'DATA'),
  ('plotly-6.1.2.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/direct_url.json',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('h2-4.2.0.dist-info/LICENSE',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/LICENSE',
   'DATA'),
  ('numpy-2.3.0.dist-info/REQUESTED',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/attrs-25.3.0.dist-info/METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/attrs-25.3.0.dist-info/WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('plotly-6.1.2.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/plotly-6.1.2.dist-info/INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/attrs-25.3.0.dist-info/INSTALLER',
   'DATA'),
  ('numpy-2.3.0.dist-info/direct_url.json',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/direct_url.json',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('numpy-2.3.0.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/numpy-2.3.0.dist-info/METADATA',
   'DATA'),
  ('h2-4.2.0.dist-info/METADATA',
   '/opt/miniconda3/envs/web-bsa/lib/python3.13/site-packages/h2-4.2.0.dist-info/METADATA',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Desktop/BSA-Test/build/bsa_app/base_library.zip',
   'DATA'),
  ('libformw.dylib', 'libformw.6.dylib', 'SYMLINK'),
  ('libreadline.dylib', 'libreadline.8.2.dylib', 'SYMLINK'),
  ('libtinfo.dylib', 'libtinfo.6.dylib', 'SYMLINK'),
  ('libiomp5.dylib', 'libomp.dylib', 'SYMLINK'),
  ('libreadline.8.dylib', 'libreadline.8.2.dylib', 'SYMLINK'),
  ('libblas.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libgfortran.dylib', 'libgfortran.5.dylib', 'SYMLINK'),
  ('libmpdec.4.dylib', 'libmpdec.4.0.0.dylib', 'SYMLINK'),
  ('libhistory.8.dylib', 'libhistory.8.2.dylib', 'SYMLINK'),
  ('libgomp.1.dylib', 'libomp.dylib', 'SYMLINK'),
  ('libquadmath.dylib', 'libquadmath.0.dylib', 'SYMLINK'),
  ('libblas.3.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libffi.dylib', 'libffi.8.dylib', 'SYMLINK'),
  ('liblapack.3.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libcrypto.dylib', 'libcrypto.3.dylib', 'SYMLINK'),
  ('libncursesw.dylib', 'libncursesw.6.dylib', 'SYMLINK'),
  ('libcblas.3.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libform.dylib', 'libform.6.dylib', 'SYMLINK'),
  ('libbz2.dylib', 'libbz2.1.0.8.dylib', 'SYMLINK'),
  ('libpanel.dylib', 'libpanel.6.dylib', 'SYMLINK'),
  ('libncurses.dylib', 'libncurses.6.dylib', 'SYMLINK'),
  ('libmenuw.dylib', 'libmenuw.6.dylib', 'SYMLINK'),
  ('libc++.dylib', 'libc++.1.0.dylib', 'SYMLINK'),
  ('libssl.dylib', 'libssl.3.dylib', 'SYMLINK'),
  ('libmenu.dylib', 'libmenu.6.dylib', 'SYMLINK'),
  ('libz.1.dylib', 'libz.1.3.1.dylib', 'SYMLINK'),
  ('libtinfow.dylib', 'libtinfow.6.dylib', 'SYMLINK'),
  ('libgomp.dylib', 'libomp.dylib', 'SYMLINK'),
  ('libsqlite3.0.dylib', 'libsqlite3.3.50.1.dylib', 'SYMLINK'),
  ('libopenblas_armv8p-r0.3.30.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libopenblasp-r0.3.30.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libsqlite3.dylib', 'libsqlite3.3.50.1.dylib', 'SYMLINK'),
  ('liblapack.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libc++.1.dylib', 'libc++.1.0.dylib', 'SYMLINK'),
  ('libexpat.1.dylib', 'libexpat.1.10.1.dylib', 'SYMLINK'),
  ('libhistory.dylib', 'libhistory.8.2.dylib', 'SYMLINK'),
  ('libpanelw.dylib', 'libpanelw.6.dylib', 'SYMLINK'),
  ('libopenblas_vortexp-r0.3.30.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libcblas.dylib', 'libopenblas.0.dylib', 'SYMLINK'),
  ('libarrow_python.2000.dylib',
   'pyarrow/libarrow_python.2000.dylib',
   'SYMLINK'),
  ('libarrow_python_parquet_encryption.2000.dylib',
   'pyarrow/libarrow_python_parquet_encryption.2000.dylib',
   'SYMLINK'),
  ('libarrow_python_flight.2000.dylib',
   'pyarrow/libarrow_python_flight.2000.dylib',
   'SYMLINK'),
  ('libsf_error_state.dylib',
   'scipy/special/libsf_error_state.dylib',
   'SYMLINK')],
 'libpython3.13.dylib',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)
