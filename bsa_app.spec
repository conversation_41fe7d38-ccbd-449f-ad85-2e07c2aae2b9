# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取项目根目录
project_root = Path.cwd()

# 定义数据文件
datas = [
    ('config.json', '.'),  # 将config.json复制到可执行文件目录
]

# 定义隐藏导入（解决一些动态导入问题）
hiddenimports = [
    'streamlit',
    'streamlit.web.cli',
    'streamlit.runtime.scriptrunner.script_runner',
    'streamlit.runtime.state',
    'streamlit.runtime.caching',
    'streamlit.components.v1',
    'pandas',
    'numpy',
    'requests',
    'openpyxl',
    'xlwt',
    'browser_cookie3',
    'dateutil',
    'dateutil.relativedelta',
    'json',
    'io',
    'datetime',
    'pathlib',
    'typing',
    'random',
]

# 排除不需要的模块以减小文件大小
excludes = [
    'tkinter',
    'matplotlib',
    'PIL',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'sphinx',
    'setuptools',
]

# 添加Python文件到数据文件
datas.extend([
    ('bsa_ui.py', '.'),
    ('bsa_core.py', '.'),
])

# 分析主程序
a = Analysis(
    ['launcher.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 创建PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='bsa_app',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保持控制台窗口以显示Streamlit输出
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
