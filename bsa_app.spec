# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取项目根目录
project_root = Path.cwd()

# 定义数据文件
datas = [
    ('config.json', '.'),  # 将config.json复制到可执行文件目录
    ('bsa_ui.py', '.'),    # 将主应用文件复制到可执行文件目录
    ('bsa_core.py', '.'),  # 将核心模块复制到可执行文件目录
]

# 定义隐藏导入（解决一些动态导入问题）
hiddenimports = [
    'streamlit',
    'streamlit.web.cli',
    'streamlit.web.server',
    'streamlit.web.server.server',
    'streamlit.runtime.scriptrunner.script_runner',
    'streamlit.runtime.state',
    'streamlit.runtime.caching',
    'streamlit.runtime.legacy_caching',
    'streamlit.components.v1',
    'streamlit.elements',
    'streamlit.elements.form',
    'streamlit.elements.widgets',
    'streamlit.runtime.uploaded_file_manager',
    'streamlit.runtime.media_file_manager',
    'streamlit.runtime.app_session',
    'streamlit.runtime.runtime',
    'streamlit.runtime.forward_msg_queue',
    'streamlit.runtime.fragment',
    'streamlit.logger',
    'streamlit.config',
    'streamlit.secrets',
    'streamlit.source_util',
    'streamlit.string_util',
    'streamlit.type_util',
    'streamlit.util',
    'streamlit.watcher',
    'pandas',
    'numpy',
    'requests',
    'openpyxl',
    'xlwt',
    'browser_cookie3',
    'dateutil',
    'dateutil.relativedelta',
    'json',
    'io',
    'datetime',
    'pathlib',
    'typing',
    'random',
    'threading',
    'webbrowser',
    'time',
]

# 排除不需要的模块以减小文件大小
excludes = [
    'tkinter',
    'matplotlib',
    'PIL',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'sphinx',
    'setuptools',
]

# 分析主程序 - 使用run_app.py作为启动器
a = Analysis(
    ['run_app.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=['hooks'],  # 添加hooks路径
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 创建PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='bsa_app',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保持控制台窗口以显示Streamlit输出
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
