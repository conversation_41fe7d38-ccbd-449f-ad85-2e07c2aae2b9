# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取项目根目录
project_root = Path.cwd()

# 定义数据文件
datas = [
    ('config.json', '.'),
    ('bsa_ui.py', '.'),
    ('bsa_core.py', '.'),
]

# 定义隐藏导入
hiddenimports = [
    'streamlit',
    'streamlit.web.cli',
    'streamlit.web.server',
    'streamlit.runtime.scriptrunner.script_runner',
    'streamlit.runtime.state',
    'streamlit.runtime.caching',
    'streamlit.components.v1',
    'pandas',
    'numpy',
    'requests',
    'openpyxl',
    'xlwt',
    'browser_cookie3',
    'dateutil',
    'dateutil.relativedelta',
]

# 排除不需要的模块
excludes = [
    'tkinter',
    'matplotlib',
    'PIL',
    'IPython',
    'jupyter',
]

# 分析主程序
a = Analysis(
    ['simple_app.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=['hooks'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='bsa_simple',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
